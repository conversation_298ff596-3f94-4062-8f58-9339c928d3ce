#ifndef NAVIGATION_PROCESS_FUSION_HPP
#define NAVIGATION_PROCESS_FUSION_HPP

#include "data_type.hpp"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "sdf_map.hpp"

#include <string>

namespace fescue_iox
{

void GetFusionGrassDetectStatus(const fescue_msgs__msg__PerceptionFusionResult &data, GrassDetectStatus &status);

void GetFusionObstacleResult(const fescue_msgs__msg__PerceptionFusionResult &data, ObstacleResult &result);

void GetFusionBoundaryResult(const fescue_msgs__msg__PerceptionFusionResult &data, BoundaryResult &result);

void GetFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result);

void GetOptFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result);

void GetDetailedFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result);

std::string GetGrassCellTypeDescription(uint8_t cell_type);

std::shared_ptr<GridMap<uint8_t>> GetRobotGridMap(const OccupancyResult &occupancy_result);

std::pair<std::shared_ptr<GridMapBase>, std::vector<std::pair<Point2f, bool>>> GetRobotMapData(const OccupancyResult &occupancy_result,
                                                                                               float x_min, float x_max, float y_min, float y_max);

std::shared_ptr<SDFMap> GetSDFMap(const OccupancyResult &occupancy_result, const Pose2f &cur_pose);

} // namespace fescue_iox

#endif
