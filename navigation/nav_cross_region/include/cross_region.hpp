#ifndef NAVIGATION_CROSS_REGION_ALG_HPP
#define NAVIGATION_CROSS_REGION_ALG_HPP

#include "data_type.hpp"
#include "imu_data_processor.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "nav_utils.hpp"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "opencv2/opencv.hpp"
#include "path_track.hpp"
#include "predict_trajectory.hpp"
#include "velocity_publisher.hpp"
#include "velocity_smooth.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>
#include <unordered_map>

namespace fescue_iox
{

// 障碍物分布结构体
struct ObstacleDistribution
{
    int left_obstacles{0};
    int center_obstacles{0};
    int right_obstacles{0};
    int left_cells{0};
    int center_cells{0};
    int right_cells{0};
    float left_ratio{0.0f};
    float center_ratio{0.0f};
    float right_ratio{0.0f};
    float total_ratio{0.0f};
};

// 综合障碍物信息结构体
struct ObstacleInfo
{
    bool is_all_grass{true};             // 是否全是草地
    bool has_left_obstacle{false};       // 左侧是否有障碍物
    bool has_right_obstacle{false};      // 右侧是否有障碍物
    bool has_left_half_obstacle{false};  // 左侧是否超过阈值面积是障碍物
    bool has_right_half_obstacle{false}; // 右侧是否超过阈值面积是障碍物
    ObstacleDistribution distribution;   // 障碍物分布详情
};

// Data time information structure for input data validation
struct DataTimeInfo
{
    // Received timestamp in microseconds
    uint64_t recv_timestamp = 0;
    // Send timestamp in microseconds
    uint64_t send_timestamp = 0;
    // Whether data frequency is too low
    bool is_low_freq = false;
    uint32_t low_freq_count = 0;
    // Whether data is timeout
    bool is_timeout = false;
};

struct CrossRegionAlgParam
{
    // Multi-region channel parameters
    float cross_region_linear{0.15};            /*param*/
    float cross_region_angular{0.5};            /*param*/
    float max_distance_threshold{0.95};         /*param*/
    float min_distance_threshold{0.65};         /*param*/
    float cross_region_special_linear{0.15};    // Linear velocity in special cases (e.g., perception-driven) /*param*/
    float cross_region_special_angular{0.2};    // Angular velocity in special cases (e.g., perception-driven) /*param*/
    float dis_tolerance{0.0};                   // Distance adjustment threshold to prevent redundant rotation /*param*/
    float cross_region_angle_compensation{0.0}; // Cross-region angle compensation /*param*/

    float channel_stop_pose_x{-0.5};               /*param*/
    int grass_count_threshold{7};                  /*param*/
    int edge_mode_direction{-1};                   // Default counterclockwise -1 /*param*/
    float channel_width{1.5};                      // Channel width /*param*/
    float camera_2_center_dis{0.37};               // Distance from the camera to the rotation center /*param*/
    float adjust_mode_x_direction_threshold{-0.3}; // X-direction threshold in adjustment mode before crossing the channel /*param*/

    // New parameters
    float mark_distance_threshold{0.5};              // Distance threshold for the beacon relative to the camera /*param*/
    int perception_drive_cooldown_time_threshold{3}; // Perception-driven cooldown time in seconds /*param*/
    float cross_region_adjust_displace{0.7};         // Adjustment displacement after crossing the region /*param*/
    float channel_fixed_distance{0.2};               // Fixed channel passing distance for FindBeaconsPhase_41_New /*param*/
};

enum class CrossRegionStatus : int
{
    InProgress = 0,
    Successed = 1,
    Failed = 2
};

enum class CorrectionState : int
{
    IDLE = 0,       // Idle state
    ACCUMULATING,   // Accumulating angle
    READY_TO_ADJUST // Ready to correct
};

/**
 * @brief Enum for current region state
 */
enum class RegionState : int
{
    IN_GRASS = 0, // Currently in the grass region
    CROSSING = 1  // Crossing the region, i.e., left the grass region
};

struct CrossRegionAlgResult
{
    bool cross_region_completed{false};
    CrossRegionStatus cross_region_status{CrossRegionStatus::InProgress};
    CrossRegionAlgResult() = default;

    CrossRegionAlgResult(bool cross_region_completed)
        : cross_region_completed(cross_region_completed)
    {
    }

    CrossRegionAlgResult(bool cross_region_completed, CrossRegionStatus status)
        : cross_region_completed(cross_region_completed)
        , cross_region_status(status)
    {
    }
};

class NavigationCrossRegionAlg
{
    using iox_exception_publisher = iox::popo::Publisher<mower_msgs::msg::SocException>;

public:
    NavigationCrossRegionAlg(const CrossRegionAlgParam &param);
    ~NavigationCrossRegionAlg();
    void DataConversion(MarkLocationResult &mark_loc_result);
    void ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result);
    void UpdateCrossRegionRunningState(CrossRegionRunningState state);
    CrossRegionAlgResult DoCrossRegion(PerceptionFusionResult &fusion_result,
                                       MarkLocationResult &mark_loc_result,
                                       ImuData &imu_data,
                                       McuExceptionStatus &mcu_exception_status);
    bool CheckInitialConditions(const PerceptionFusionResult &fusion_result);
    void SetCrossRegionAlgParam(const CrossRegionAlgParam &param);
    void GetCrossRegionAlgParam(CrossRegionAlgParam &param);
    void SetMarkLocationResult(const MarkLocationResult &mark_location_result);
    void SetMarkLocationMarkIdCallback(std::function<bool(int)> callback);
    void ProhibitVelPublisher();
    void ResetCrossRegionFlags();
    void SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback);
    void SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback);
    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);

    // Data validation methods
    bool CheckPerceptionFusionDataError(const PerceptionFusionResult &fusion_result);
    bool CheckMarkLocationDataError(const MarkLocationResult &mark_loc_result);
    bool CheckImuDataError();
    bool CheckMcuExceptionDataError();
    bool CheckMotorSpeedDataError();
    void CheckTimeout(const std::string &name, const DataTimeInfo &last_time_info,
                      uint64_t timeout, DataTimeInfo &cur_time_info);
    void UpdateDataTimeInfo(const std::string &name, const DataTimeInfo &last_time_info,
                            uint64_t low_freq_time, uint32_t low_freq_count_max,
                            uint64_t cur_send_timestamp, DataTimeInfo &cur_time_info);
    void SetPerceptionFusionResult(const PerceptionFusionResult &fusion_result);
    void SetMarkLocationResultWithTimeInfo(const MarkLocationResult &mark_loc_result);
    void SetImuDataWithTimeInfo(const ImuData &imu_data);
    void SetMcuExceptionWithTimeInfo(const McuExceptionStatus &mcu_exception_status);
    void SetMotorSpeedDataWithTimeInfo(const MotorSpeedData &motor_speed_data);

    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }
    void SetAlgoRunningState(MowerRunningState state);
    const char *GetVersion();
    void PublishVelocityAndInterferenceCorrection(ImuData &imu_data);

    void CalibrateImuBias(const ImuData &imu_data);

    // IMU data processor interface
    void SetImuData(const ImuData &imu_data);
    void InitializeImuProcessor();
    void ShutdownImuProcessor();
    void StopContinuousLinearMotion();

private:
    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);
    void PublishZeroVelocity();
    void PauseVelocity();
    void ResumeVelocity();

private:
    // 通用的区域障碍物检测函数
    // region_type: 0-全部区域, 1-左侧区域, 2-右侧区域, 3-中心区域, 4-左半区域, 5-右半区域
    bool DetectObstacleInRegion(const std::vector<std::vector<uint8_t>> &grid, int height, int width,
                                int region_type, float threshold);

    // 检测特定区域的障碍物分布
    ObstacleDistribution GetObstacleDistribution(const std::vector<std::vector<uint8_t>> &grid, int height, int width);

    // 一次性获取所有障碍物信息
    ObstacleInfo DetectAllObstacles(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float resolution);

    // 检查是否全是草地（无障碍物）
    bool IsAllGrass(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    bool HasLeftHalfGrass(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    bool IsAllObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    // 检查左侧是否有障碍物
    bool HasLeftObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    // 检查右侧是否有障碍物
    bool HasRightObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    // 检查左侧是否超过一半面积是障碍物
    bool HasLeftHalfObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    // 检查右侧是否超过一半面积是障碍物
    bool HasRightHalfObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    // 检查前方是否有障碍物
    bool HasForwardObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

    bool HasCenterObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold);

private:
    void DealFeatureSelect(ThreadControl control, bool state);
    void EdgeFollowDisable();
    void EdgeFollowEnable();

    void FindBeaconsPhase_1(const MarkLocationResult &mark_loc_result);
    bool ProcessPassageRotate(int detect_status);
    void GetPrecisePositionOfBeacon(MarkLocationResult &mark_loc_result, float &x_first, float &y_first,
                                    float &yaw_first, bool &is_get_precise_position);
    void CorrectionOutsideConfidence(const MarkLocationResult &mark_loc_result);
    void ClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first);
    void ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular);
    void ControlRotaryMotionWithIMU(const float &yaw_des, const float &yaw_first, const float &vel_angular);
    void ControlLinearMotion(const float &pass_point, const float &location, const float &vel_linear, const int &reverse);
    void ControlLinearMotionWithIMU(const float &pass_point, const float &location, const float &vel_linear, const int &reverse, const float &target_yaw = 0.0f);
    void PublishVelocityWithIMUCorrection(float expect_linear_velocity, const float &target_yaw = 0.0f);
    void ControlLinearMotionWithIMUThread(const float &pass_point, const float &location, const float &vel_linear, const int &reverse, const float &target_yaw = 0.0f);
    void CounterClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first);
    bool Nongrass2Grass(const PerceptionFusionResult &fusion_result);
    bool Grass2Nongrass(const PerceptionFusionResult &fusion_result);
    bool CrossRegionFinished(const PerceptionFusionResult &fusion_result);
    void CounterClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first);
    void ClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first);
    void PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result);
    void DealCrossRegionPhase_1(MarkLocationResult &mark_loc_result);
    void DealCrossRegionPhase_2(MarkLocationResult &mark_loc_result, float &x_first, float &y_first, float &yaw_first);
    CrossRegionAlgResult DealCrossRegionPhase_3(MarkLocationResult &mark_loc_result,
                                                const float &x_first, const float &y_first, const float &yaw_first);
    CrossRegionAlgResult DealCrossRegionPhase_4(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result,
                                                float &act_linear);
    CrossRegionAlgResult DealCrossRegionPhaseWithBEV_4(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result,
                                                       float &act_linear);

    // New
    void HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active);
    void HandleCrossRegionPerceptionBeaconDetection(const MarkLocationResult &mark_loc_result,
                                                    bool &is_cooldown_active);
    void FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx);
    void FingVaidBeaconPairIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx, int &paired_beacon_idx);
    bool SetMarkLocationMarkId(int mark_id);
    void HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                     std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold);
    void HandleCrossRegionCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                            std::chrono::seconds &perception_drive_duration,
                                            int &perception_drive_cooldown_time_threshold);
    void ResetAndActivateCooldown();
    void DealStage4EnteringPassage(const float &x_first, const float &y_first, const float &yaw_first,
                                   float &act_linear);
    void DealCrossRegionPhase_41(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result,
                                 bool &cross_region_completed, float &act_linear);

    // void DealCrossRegionPhaseWithBEV_41(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result, bool &cross_region_completed, float &act_linear);

    void UpdateStraightMotionTimingNew(float act_linear);
    void UpdateStraightMotionTiming(float act_linear);
    void DealCrossRegionPhase_42(MarkLocationResult &mark_loc_result, float &x_first, float &y_first, float &yaw_first);
    void DealCrossRegionPhase_43(MarkLocationResult &mark_loc_result, const float &x_first, const float &y_first, const float &yaw_first);
    void DealCrossRegionPhase_44(MarkLocationResult &mark_loc_result, bool &cross_region_completed, const float &x_first);
    void FindBeaconsPhase_41(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                             bool &cross_region_completed);

    void FindBeaconsPhase_41_New(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                                 bool &cross_region_completed, float &act_linear);

    void FindBeaconsPhaseWithBEV_41_New(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                                        bool &cross_region_completed, float &act_linear);

    void BeaconDetectedPhase_4(const MarkLocationResult &mark_loc_result, bool &beacon_detect_completed);
    void TimedMovementWithBeaconDetection(float target_distance, const MarkLocationResult &mark_loc_result,
                                          bool &beacon_found, bool &movement_completed,
                                          float &act_linear);

    void TimedMovementWithBEV(float target_distance, const MarkLocationResult &mark_loc_result,
                              bool &movement_completed,
                              float &act_linear);

    // Added on 0225
    void ComputeB(float A_x, float A_y, float yaw, float B_y, float &B_x, float &distance);
    void ControlProcessToPassPoints(const float &x_first, const float &y_first, const float &yaw_first);
    void ControlProcessToEndPoints(const float &x_first, const float &y_first, const float &yaw_first);

    // IMU-based control functions
    void ControlProcessToPassPointsWithIMU(const float &x_first, const float &y_first, const float &yaw_first);
    void ControlProcessToEndPointsWithIMU(const float &x_first, const float &y_first, const float &yaw_first);
    int PairNumber(int n);
    void SafeLinearMotion(float target_dis, float current_dis,
                          float vel_linear, int reverse,
                          MarkLocationResult &mark_loc_result);
    void SafeLinearMotionWithNonGrassDetection(float target_dis, float current_dis, float vel_linear, int reverse, MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result, bool &motion_completed);
    void CheckFrontBeaconCollision(const MarkLocationResult &mark_loc_result);

    // MCU exception handling functions
    void HandleMcuException();
    void HandleNormalCrossRegionStates();
    CrossRegionAlgResult HandleNormalOperation(PerceptionFusionResult &fusion_result, MarkLocationResult &mark_loc_result);
    void ProcessRecoveryException();

private:
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void InitPublisher();

private:
    std::function<void(const std::vector<FeatureSelectData> &)> feature_select_callback_;
    std::function<void(CrossRegionRunningState)> cross_region_running_state_callback_;
    std::function<bool(int)> set_mark_id_callback_;

    // Running state variables
    MowerRunningState mower_running_state_{MowerRunningState::STOP}; // 0-Not started, 1-Running, 2-Paused

    bool phase_1_completed_ = false; //! Phase 1: Enable edge following until a beacon QR code is detected
    bool phase_2_completed_ = false; //! Phase 2: Find a high-confidence area based on positioning confidence intervals
    bool phase_3_completed_ = false; //! Phase 3: Adjust position after obtaining precise pose

    bool phase_41_completed_ = false;
    bool phase_42_completed_ = false;
    bool phase_43_completed_ = false;

    std::deque<GrassDetectStatus> frames_;
    bool is_walking_before_crossing_passage_{false};

    // Determine if transitioning from grass to non-grass
    uint64_t passage_rotate_time_{0};
    uint64_t passage_rotate_begin_time_{0};

    // Algorithm runtime variables
    bool is_cooldown_active_ = false;                                       // Controls whether cooldown mechanism is active
    std::chrono::time_point<std::chrono::steady_clock> last_cooldown_time_; // Records cooldown start time
    std::chrono::seconds perception_drive_duration_{0};
    RegionState current_state_{RegionState::IN_GRASS};
    int next_paired_beacon_id_ = -1;      // Next paired beacon ID
    bool non_grass_area_reached_ = false; // New flag for phase 4
    bool emergency_stop_ = false;         // Emergency stop flag
    bool front_beacon_detected_ = false;  // Safety control function: whether the front beacon is detected
    bool is_first_non_grass_area_reached_ = true;

    /*******************************************************Algorithm Parameters********************************************************************/
    // Multi-region channel parameters
    float cross_region_linear_{0.15};            /*param*/
    float cross_region_angular_{0.5};            /*param*/
    float max_distance_threshold_{0.95};         /*0.95param*/
    float min_distance_threshold_{0.65};         /*0.65param*/
    float cross_region_special_linear_{0.15};    // Linear velocity in special cases (e.g., perception-driven) /*param*/
    float cross_region_special_angular_{0.2};    // Angular velocity in special cases (e.g., perception-driven) /*param*/
    float dis_tolerance_{0.0};                   // Distance adjustment threshold to prevent redundant rotation /*param*/
    float cross_region_angle_compensation_{0.0}; // Cross-region angle compensation /*param*/

    float channel_stop_pose_x_{-0.5};               /*param*/
    int grass_count_threshold_{7};                  /*param*/
    int edge_mode_direction_{-1};                   // Default counterclockwise -1 /*param*/
    float channel_width_{1.5};                      // Channel width /*param*/
    float camera_2_center_dis_{0.37};               // Distance from the camera to the rotation center /*param*/
    float adjust_mode_x_direction_threshold_{-0.3}; // X-direction threshold in adjustment mode before crossing the channel /*param*/

    // New parameters
    float mark_distance_threshold_{0.5};              // Distance threshold for the beacon relative to the camera /*param*/
    int perception_drive_cooldown_time_threshold_{3}; // Perception-driven cooldown time in seconds /*param*/
    float cross_region_adjust_displace_{0.7};         // Adjustment displacement after crossing the region /*param*/

    /*******************************************************Algorithm Parameters********************************************************************/

    // Beacon detection variables
    CrossRegionRunningState cross_region_state_{CrossRegionRunningState::UNDEFINED};
    std::mutex cross_region_mutex_;

    // Velocity publisher
    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr};

    bool apply_coordinate_transform_{true};
    std::mutex mark_loc_mutex_;
    MarkLocationResult mark_loc_result_;
    MarkLocationResult mark_loc_result_law_;

private:
    std::chrono::steady_clock::time_point straight_start_time_; // Start time of straight-line motion
    bool is_straight_timing_started_ = false;                   // Flag indicating whether timing has started
    static constexpr int CROSS_AREA_WALKING_TIMEOUT{300};       // 5 minutes /*param*/
    std::unique_ptr<iox_exception_publisher> pub_exception_;

private:
    bool is_first_linear_in_progress_ = false;                // Whether the first straight motion is in progress
    bool linear_motion_completed_ = false;                    // Whether the straight motion is completed
    std::chrono::steady_clock::time_point linear_start_time_; // Start time of straight motion

    // Timed movement with beacon detection variables (now displacement-based)
    bool timed_movement_in_progress_ = false;                               // Whether displacement-based movement is in progress
    float timed_movement_accumulated_distance_ = 0.0f;                      // Accumulated distance for displacement-based movement
    std::chrono::steady_clock::time_point timed_movement_last_update_time_; // Last update time for displacement-based movement

private:
    uint64_t last_imu_timestamp_{0};
    float threshold_angular_velocity_{0.1};                 // rad/s, interference detection threshold
    float threshold_accumulated_angle_{5.0 * M_PI / 180.0}; // 5 degrees, converted to radians
    float threshold_correction_{5.0 * M_PI / 180.0};        // 5 degrees, converted to radians
    float accumulated_angle_{0.0};                          // Accumulated heading change caused by interference
    bool is_correcting_{false};                             // Correction state flag
    float yaw_target_{0.0};                                 // Target heading angle for straight motion
    float yaw_current_{0.0};                                // Current heading angle (IMU integration)
    std::chrono::steady_clock::time_point last_imu_time_;   // Last IMU update time
    bool is_first_imu_{true};                               // Flag indicating if this is the first IMU data processing

    float bias_z_{0.0f};                 // Estimated zero bias of z-axis angular velocity
    float bias_threshold_{0.05};         // Angular velocity filtering threshold (rad/s)
    std::vector<float> bias_samples_;    // Samples collected during calibration
    const size_t bias_sample_count_{50}; // Number of calibration samples (e.g., 0.5 seconds @ 50Hz)
    bool is_bias_calibrated_{false};     // Zero bias calibration completion flag

private:
    float accumulated_distance_{0.0f};
    std::chrono::steady_clock::time_point last_distance_update_time_;
    float cross_region_distance_threshold_{8.0}; // 8-meter distance threshold /*param*/

private:
    std::chrono::steady_clock::time_point beacon_pairing_error_start_time_;
    bool beacon_pairing_error_active_{false};

private:
    std::mutex motor_speed_mtx_;
    MotorSpeedData motor_speed_data_;
    float wheel_base_ = 0.335f;
    float wheel_radius_ = 0.1f; // Wheel radius (meters)

private:
    float grass_area_accumulated_distance_{0.0f};                           // Accumulated distance in grass area
    std::chrono::steady_clock::time_point last_grass_distance_update_time_; // Last update time for grass area distance

private:
    // Channel distance tracking variables for FindBeaconsPhase_41_New
    bool channel_distance_tracking_started_{false};                           // Flag to track if channel distance tracking has started
    float channel_accumulated_distance_{0.0f};                                // Accumulated distance in channel
    float channel_fixed_distance_{0.2f};                                      // Fixed channel passing distance parameter (default 3.0m) /*param*/
    std::chrono::steady_clock::time_point channel_distance_start_time_;       // Start time for channel distance tracking
    std::chrono::steady_clock::time_point channel_last_distance_update_time_; // Last update time for channel distance

private:
    std::mutex imu_data_mtx_;
    ImuData imu_data_;

    float act_linear_{0.0f};
    float act_angular_{0.0f};

private:
    // IMU data processor
    std::unique_ptr<ImuDataProcessor> imu_processor_;
    ImuProcessorParam imu_processor_param_;

    float x_after_adjustment_{0.0};

private:
    // MCU exception handling variables
    int mcu_exception_retry_count_{0};                          // MCU exception retry count
    int max_mcu_exception_retries_ = 3;                         // Maximum retry count
    float mcu_exception_backup_distance_ = 0.2f;                // Backup distance (m)
    float mcu_exception_backup_speed_ = -0.1f;                  // Backup speed (m/s)
    int mcu_exception_backup_duration_ = 2000;                  // Backup duration (ms)
    std::chrono::steady_clock::time_point last_exception_time_; // Last exception handling time

    float mark_id_distance_temp_ = 0.0f;

    bool get_pose_stage_{false};

private:
    // Data time information management for input data validation
    std::mutex data_time_info_map_mtx_;
    std::unordered_map<std::string, DataTimeInfo> data_time_info_map_;

    float obstacle_ratio_threshold_{0.5f}; // 障碍物占比阈值/*param*/
};

} // namespace fescue_iox

#endif
